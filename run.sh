#!/bin/bash

# Set error handling
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install Python if not exists
install_python() {
    if ! command_exists python3; then
        echo -e "${YELLOW}Python3 tidak ditemukan. Menginstal Python3...${NC}"
        sudo apt-get update
        sudo apt-get install -y python3 python3-venv python3-pip
    fi
}

# Function to setup virtual environment
setup_venv() {
    echo -e "${GREEN}Menyiapkan virtual environment...${NC}"
    python3 -m venv venv
    source venv/bin/activate
    
    echo -e "${GREEN}Menginstal dependensi...${NC}"
    pip install --upgrade pip
    pip install -r requirements.txt
    
    # Install python-dotenv if not in requirements.txt
    pip install python-dotenv
    
    echo -e "${GREEN}Virtual environment siap!${NC}"
}

# Function to run the bot
run_bot() {
    echo -e "${GREEN}Menjalankan bot...${NC}"
    if [ -f ".env" ]; then
        source venv/bin/activate
        python3 bot.py
    else
        echo -e "${YELLOW}File .env tidak ditemukan. Pastikan file .env sudah ada di direktori ini.${NC}"
        echo -e "Buat file .env dengan format:"
        echo "PRIVATE_KEYS=privatekey1,privatekey2"
        exit 1
    fi
}

# Main script
echo -e "${GREEN}=== PHAROS BOT INSTALLER & RUNNER ===${NC}"

# Check Python
install_python

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    setup_venv
else
    echo -e "${GREEN}Virtual environment sudah ada. Mengaktifkan...${NC}"
    source venv/bin/activate
    
    # Check if requirements are installed
    if ! pip list | grep -F web3 > /dev/null || ! pip list | grep -F python-dotenv > /dev/null; then
        echo -e "${YELLOW}Beberapa dependensi belum terinstal. Menginstal...${NC}"
        pip install -r requirements.txt
        pip install python-dotenv
    fi
fi

# Run the bot
run_bot

deactivate
