#!/usr/bin/env python3
"""
Script untuk generate wallet baru atau validasi private key yang sudah ada
Untuk bot Pharos Network
"""

import os
import sys
from eth_account import Account
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

def generate_new_wallet():
    """Generate wallet baru"""
    account = Account.create()
    return {
        'private_key': account.key.hex(),
        'address': account.address
    }

def validate_private_key(private_key):
    """Validasi private key"""
    try:
        # Remove 0x prefix if exists
        if private_key.startswith('0x'):
            private_key = private_key[2:]
        
        # Add 0x prefix
        private_key = '0x' + private_key
        
        # Try to create account
        account = Account.from_key(private_key)
        return {
            'valid': True,
            'address': account.address,
            'private_key': private_key
        }
    except Exception as e:
        return {
            'valid': False,
            'error': str(e)
        }

def main():
    console.print(Panel.fit(
        "[bold blue]🔐 Pharos Network Wallet Generator & Validator[/bold blue]",
        border_style="blue"
    ))
    
    while True:
        console.print("\n[bold yellow]Pilih opsi:[/bold yellow]")
        console.print("1. Generate wallet baru")
        console.print("2. Validasi private key yang sudah ada")
        console.print("3. Keluar")
        
        choice = console.input("\n[bold cyan]Masukkan pilihan (1-3): [/bold cyan]")
        
        if choice == "1":
            console.print("\n[bold green]🔄 Generating wallet baru...[/bold green]")
            wallet = generate_new_wallet()
            
            table = Table(title="Wallet Baru")
            table.add_column("Field", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Private Key", wallet['private_key'])
            table.add_row("Address", wallet['address'])
            
            console.print(table)
            
            console.print("\n[bold red]⚠️  PENTING:[/bold red]")
            console.print("- Simpan private key dengan aman")
            console.print("- Jangan bagikan private key kepada siapa pun")
            console.print("- Backup private key di tempat yang aman")
            
            save = console.input("\n[yellow]Simpan ke file .env? (y/n): [/yellow]")
            if save.lower() == 'y':
                save_to_env(wallet['private_key'])
        
        elif choice == "2":
            private_key = console.input("\n[bold cyan]Masukkan private key: [/bold cyan]")
            result = validate_private_key(private_key)
            
            if result['valid']:
                console.print(f"\n[bold green]✅ Private key valid![/bold green]")
                console.print(f"[cyan]Address: {result['address']}[/cyan]")
                console.print(f"[dim]Formatted key: {result['private_key']}[/dim]")
            else:
                console.print(f"\n[bold red]❌ Private key tidak valid![/bold red]")
                console.print(f"[red]Error: {result['error']}[/red]")
        
        elif choice == "3":
            console.print("\n[bold blue]👋 Selamat tinggal![/bold blue]")
            break
        
        else:
            console.print("\n[bold red]❌ Pilihan tidak valid![/bold red]")

def save_to_env(private_key):
    """Simpan private key ke file .env"""
    try:
        # Read existing .env
        env_content = ""
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                env_content = f.read()
        
        # Update PRIVATE_KEYS line
        lines = env_content.split('\n')
        updated = False
        
        for i, line in enumerate(lines):
            if line.startswith('PRIVATE_KEYS='):
                current_keys = line.split('=', 1)[1].strip()
                if current_keys and current_keys not in ['', 'YOUR_PRIVATE_KEY_1,YOUR_PRIVATE_KEY_2']:
                    # Add to existing keys
                    lines[i] = f"PRIVATE_KEYS={current_keys},{private_key}"
                else:
                    # Replace empty or placeholder keys
                    lines[i] = f"PRIVATE_KEYS={private_key}"
                updated = True
                break
        
        if not updated:
            # Add new line if PRIVATE_KEYS not found
            lines.append(f"PRIVATE_KEYS={private_key}")
        
        # Write back to .env
        with open('.env', 'w') as f:
            f.write('\n'.join(lines))
        
        console.print(f"\n[bold green]✅ Private key berhasil disimpan ke .env[/bold green]")
        
    except Exception as e:
        console.print(f"\n[bold red]❌ Gagal menyimpan ke .env: {e}[/bold red]")

if __name__ == "__main__":
    main()
