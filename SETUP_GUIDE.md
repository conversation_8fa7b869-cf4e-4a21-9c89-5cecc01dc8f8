# 🚀 Pharos Network Bot - Setup Guide

## ❌ Masalah yang Anda Alami

Bot menampilkan error:
```
❌ Private key [Akun 1] tidak valid. Melewati.
❌ Private key [Akun 2] tidak valid. Melewati.
```

## 🔍 Penyebab Masalah

File `.env` masih menggunakan placeholder values:
```
PRIVATE_KEYS=YOUR_PRIVATE_KEY_1,YOUR_PRIVATE_KEY_2
```

## ✅ Solusi

### Opsi 1: Manual Edit File .env

1. Buka file `.env` di direktori ini
2. Ganti baris `PRIVATE_KEYS=` dengan private key asli Anda:

```bash
# Untuk 1 wallet:
PRIVATE_KEYS=0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef

# Untuk multiple wallets (pisahkan dengan koma, tanpa spasi):
PRIVATE_KEYS=0x1234...abcdef,0x5678...9012ef,0x9abc...def345
```

### Opsi 2: Menggunakan Generator Script

Jalankan script helper:
```bash
python3 generate_wallet.py
```

Script ini bisa:
- Generate wallet baru
- Validasi private key yang sudah ada
- Otomatis menyimpan ke file .env

### Opsi 3: Generate Wallet Baru (Manual)

Jika Anda belum punya wallet, buat wallet baru:

```python
from eth_account import Account
account = Account.create()
print(f"Private Key: {account.key.hex()}")
print(f"Address: {account.address}")
```

## 📋 Format Private Key yang Benar

✅ **Format yang BENAR:**
- `0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef` (66 karakter dengan 0x)
- `1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef` (64 karakter tanpa 0x)

❌ **Format yang SALAH:**
- `YOUR_PRIVATE_KEY_1` (placeholder)
- `0x123` (terlalu pendek)
- `private key dengan spasi`

## 🔒 Keamanan

⚠️ **PENTING:**
- Jangan bagikan private key kepada siapa pun
- Backup private key di tempat yang aman
- File `.env` sudah ada di `.gitignore` untuk keamanan

## 🚀 Menjalankan Bot Setelah Setup

Setelah mengisi private key yang benar:

```bash
./run.sh
```

Bot akan:
1. ✅ Berhasil memuat private keys
2. 🔗 Terhubung ke RPC Node
3. 🚀 Mulai menjalankan swap dan liquidity operations

## 🛠️ Troubleshooting

### Error: "Private key tidak valid"
- Pastikan format private key benar (64/66 karakter hex)
- Pastikan tidak ada spasi di private key
- Pastikan tidak menggunakan placeholder values

### Error: "Tidak ada private key yang ditemukan"
- Pastikan file `.env` ada di direktori yang sama dengan `bot.py`
- Pastikan baris `PRIVATE_KEYS=` tidak kosong

### Error: "Gagal terkoneksi ke RPC Node"
- Periksa koneksi internet
- RPC node mungkin sedang down, coba lagi nanti

## 📞 Support

Jika masih ada masalah, periksa:
1. Format private key sudah benar
2. File `.env` ada dan tidak kosong
3. Dependencies sudah terinstall (`pip install -r requirements.txt`)
