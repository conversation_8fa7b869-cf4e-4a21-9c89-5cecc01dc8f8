#!/bin/bash

# Change to the project directory
cd /home/<USER>/testnet/daily/pharos-network

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install --save-dev hardhat @nomicfoundation/hardhat-toolbox
    npm install
fi

# Check if .env exists
if [ ! -f ".env" ]; then
    echo "Error: .env file not found. Please create it first."
    exit 1
fi

# Run the bot
echo "Starting the bot..."
npx hardhat run scripts/main.ts --network pharos-testnet-officialrpc
